# WordPress Plugin Integration Guide
## Dakoii Provincial Administration Manager - Website Features Implementation

### Overview
This guide outlines how to strategically implement the requested website features while maintaining smooth integration with existing WordPress functionality and the current plugin architecture.

### Current Plugin Foundation
The plugin already provides:
- **Custom Post Types**: `esp_governor`, `esp_mp`, `esp_district`, `esp_event`, `esp_news`
- **Shortcode System**: Multiple naming conventions for flexibility
- **Admin Interface**: Dashboard and management pages
- **Frontend Display**: Styled components with PNG color scheme
- **Meta Fields**: Structured data storage for each content type

---

## Implementation Strategy

### Phase 1: Core Extensions (Immediate Implementation)

#### 1.1 Extend Existing Post Types

**Districts Enhancement** - Add to `class-provincial-meta-boxes.php`:

```php
// Add these new meta fields to district_meta_box_callback()
$district_pm = get_post_meta($post->ID, '_esp_district_pm', true);
$district_admin = get_post_meta($post->ID, '_esp_district_admin', true);
$district_statement = get_post_meta($post->ID, '_esp_district_statement', true);
$district_structure = get_post_meta($post->ID, '_esp_district_structure', true);
$district_map = get_post_meta($post->ID, '_esp_district_map', true);
$district_profile = get_post_meta($post->ID, '_esp_district_profile', true);
```

**Events Enhancement** - Already has most required fields:
- ✅ Banner/Flyer Image (featured image)
- ✅ Date and Time (start/end dates)
- ✅ Venue (location field)
- ✅ Description (post content)
- ✅ Title (post title)

#### 1.2 New Post Types Required

**Sectors Post Type** - Add to `class-provincial-post-types.php`:

```php
private function register_sector_post_type() {
    register_post_type('esp_sector', array(
        'public'             => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'supports'           => array('title', 'editor', 'thumbnail'),
        'labels'             => array(
            'name'               => 'Sectors',
            'singular_name'      => 'Sector',
            'add_new_item'       => 'Add New Sector',
            'edit_item'          => 'Edit Sector',
            'all_items'          => 'All Sectors',
        ),
    ));
}
```

**Banners Post Type** - For special home page banners:

```php
private function register_banner_post_type() {
    register_post_type('esp_banner', array(
        'public'             => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'supports'           => array('title', 'editor', 'thumbnail'),
        'labels'             => array(
            'name'               => 'Banners',
            'singular_name'      => 'Banner',
        ),
    ));
}
```

#### 1.3 New Shortcodes Implementation

**Home Page Shortcodes** - Add to `class-provincial-shortcodes.php`:

```php
// Home Page Components
add_shortcode('dakoii_home_banner', array($this, 'home_banner_shortcode'));
add_shortcode('dakoii_home_hero', array($this, 'home_hero_shortcode'));
add_shortcode('dakoii_featured_content', array($this, 'featured_content_shortcode'));
add_shortcode('dakoii_home_governor', array($this, 'home_governor_shortcode'));

// Sectors
add_shortcode('dakoii_sectors', array($this, 'sectors_shortcode'));
add_shortcode('dakoii_sector', array($this, 'single_sector_shortcode'));

// Enhanced Districts
add_shortcode('dakoii_district_hero', array($this, 'district_hero_shortcode'));
add_shortcode('dakoii_district_profile', array($this, 'district_profile_shortcode'));
add_shortcode('dakoii_district_leadership', array($this, 'district_leadership_shortcode'));

// Profile Page
add_shortcode('dakoii_provincial_profile', array($this, 'provincial_profile_shortcode'));
add_shortcode('dakoii_administrators', array($this, 'administrators_shortcode'));
```

### Phase 2: Advanced Features

#### 2.1 Administrative Roles System

**New Post Types for Administrative Staff**:
- `esp_provincial_admin` - Provincial Administrator
- `esp_deputy_admin` - Deputy Provincial Administrators  
- `esp_executive_manager` - Executive Managers
- `esp_district_admin` - District Administrators

#### 2.2 Enhanced Data Storage

**Flexible Profile Information** - Using JSON meta fields:

```php
// District Profile Information Structure
$district_profile = array(
    'demographics' => array(
        array('item' => 'Population', 'description' => 'Total residents', 'number' => '75,000', 'image' => ''),
        array('item' => 'Area', 'description' => 'Total land area', 'number' => '2,500 km²', 'image' => ''),
    ),
    'infrastructure' => array(
        array('item' => 'Schools', 'description' => 'Primary and secondary', 'number' => '45', 'image' => ''),
        array('item' => 'Health Centers', 'description' => 'Medical facilities', 'number' => '12', 'image' => ''),
    )
);
```

---

## Detailed Implementation Guide

### 1. Home Page Implementation

**Required Shortcodes for Home Page:**

```html
<!-- Special Banner (conditional) -->
[dakoii_home_banner]

<!-- Hero Slideshow with Text -->
[dakoii_home_hero slides="5" auto_play="true" show_text="true"]

<!-- Governor's Leadership Message -->
[dakoii_home_governor show_photo="true" show_message="true"]

<!-- MPs List -->
[dakoii_mps limit="6" show_photos="true" layout="grid"]

<!-- Featured News and Events -->
[dakoii_featured_content news_limit="3" events_limit="3"]
```

### 2. Sectors Page Implementation

**Sector Display Structure:**
```html
<!-- All Sectors Overview -->
[dakoii_sectors layout="grid" show_leadership="true"]

<!-- Individual Sector (for single sector pages) -->
[dakoii_sector id="123" show_leadership="true" show_structure="true" show_statement="true"]
```

**Sector Meta Fields:**
- Leadership Name & Title
- Leadership Message
- Functional Statement
- Sector Structure (hierarchical)
- Contact Information

### 3. Districts Page Implementation

**Enhanced District Display:**
```html
<!-- District Hero Slideshow -->
[dakoii_district_hero district_id="123" slides="3"]

<!-- District Leadership (Political + Administrative) -->
[dakoii_district_leadership district_id="123" show_both="true"]

<!-- Complete District Profile -->
[dakoii_district_profile district_id="123" show_map="true" show_stats="true"]

<!-- District News and Events -->
[dakoii_news district_id="123" limit="5"]
[dakoii_events district_id="123" limit="5"]
```

### 4. ESP Profile Page Implementation

**Complete Provincial Profile:**
```html
<!-- Provincial Leadership -->
[dakoii_governor show_message="true"]
[dakoii_administrators type="provincial" show_message="true"]

<!-- All MPs -->
[dakoii_mps layout="list" show_all="true"]

<!-- Administrative Structure -->
[dakoii_administrators type="deputy" layout="grid"]
[dakoii_administrators type="executive" layout="grid"]
[dakoii_administrators type="district" layout="list"]

<!-- Provincial Information -->
[dakoii_provincial_profile show_writeup="true" show_structure="true" show_map="true"]
```

---

## Technical Implementation Details

### 1. Database Schema Extensions

**New Meta Fields for Districts:**
```php
'_esp_district_pm'          // District PM name and details
'_esp_district_admin'       // District Administrator details  
'_esp_district_statement'   // District statement text
'_esp_district_structure'   // District organizational structure
'_esp_district_map'         // Map image or embed code
'_esp_district_profile'     // JSON array of profile information
```

**New Meta Fields for Sectors:**
```php
'_esp_sector_leadership'    // Leadership details
'_esp_sector_statement'     // Functional statement
'_esp_sector_structure'     // Organizational structure
'_esp_sector_contact'       // Contact information
```

### 2. Admin Interface Extensions

**New Admin Menu Items:**
- Sectors Management
- Banners Management  
- Administrators Management
- Enhanced District Management

**Admin Page Structure:**
```php
// Add to class-provincial-admin.php
add_submenu_page(
    'provincial-admin-dashboard',
    __('Sectors', 'esp-admin-manager'),
    __('Sectors', 'esp-admin-manager'),
    'edit_posts',
    'provincial-admin-sectors',
    array($this, 'sectors_page')
);
```

### 3. Frontend Styling Extensions

**New CSS Classes** (add to `public-style.css`):
```css
/* Home Page Components */
.esp-home-banner { /* Banner styling */ }
.esp-home-hero { /* Hero slideshow styling */ }
.esp-featured-content { /* Featured content grid */ }

/* Sectors */
.esp-sectors-grid { /* Sectors grid layout */ }
.esp-sector-card { /* Individual sector cards */ }

/* Enhanced Districts */
.esp-district-hero { /* District slideshow */ }
.esp-district-leadership { /* Leadership display */ }
.esp-district-profile { /* Profile information */ }

/* Profile Page */
.esp-provincial-profile { /* Complete profile layout */ }
.esp-administrators-grid { /* Administrators display */ }
```

---

## Integration Best Practices

### 1. WordPress Theme Integration

**Template Hierarchy Support:**
```php
// Add to class-provincial-frontend.php
add_filter('template_include', array($this, 'load_custom_templates'));

public function load_custom_templates($template) {
    if (is_singular('esp_sector')) {
        $custom_template = locate_template('single-esp_sector.php');
        if ($custom_template) {
            return $custom_template;
        }
    }
    return $template;
}
```

### 2. Hooks and Filters for Customization

**Action Hooks:**
```php
do_action('esp_before_district_profile', $district_id);
do_action('esp_after_sector_display', $sector_id);
do_action('esp_home_banner_display', $banner_id);
```

**Filter Hooks:**
```php
$content = apply_filters('esp_district_profile_content', $content, $district_id);
$leadership = apply_filters('esp_sector_leadership_display', $leadership, $sector_id);
```

### 3. Responsive Design Considerations

**Mobile-First Approach:**
```css
/* Mobile styles first */
.esp-districts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

/* Tablet and up */
@media (min-width: 768px) {
    .esp-districts-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .esp-districts-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
```

---

## Implementation Timeline

### Week 1: Foundation
- [ ] Extend existing post types with new meta fields
- [ ] Create new post types (sectors, banners)
- [ ] Add basic shortcodes for home page components

### Week 2: Core Features
- [ ] Implement sectors functionality
- [ ] Enhance district displays
- [ ] Create administrative roles system

### Week 3: Advanced Features
- [ ] Complete profile page implementation
- [ ] Add template integration
- [ ] Implement responsive styling

### Week 4: Testing & Polish
- [ ] Cross-browser testing
- [ ] Mobile responsiveness testing
- [ ] Performance optimization
- [ ] Documentation completion

---

## Usage Examples

### Creating a Complete Home Page
```html
<!-- In your WordPress page/post editor -->
[dakoii_home_banner]

[dakoii_home_hero slides="5" auto_play="true" interval="6000"]

<h2>Governor's Message</h2>
[dakoii_home_governor show_photo="true" show_message="true"]

<h2>Our Representatives</h2>
[dakoii_mps limit="6" show_photos="true" layout="grid"]

<h2>Latest Updates</h2>
[dakoii_featured_content news_limit="3" events_limit="3" layout="mixed"]
```

### Creating a District Page
```html
[dakoii_district_hero district_id="123"]

<h2>District Leadership</h2>
[dakoii_district_leadership district_id="123" show_both="true"]

<h2>About Our District</h2>
[dakoii_district_profile district_id="123" show_map="true" show_stats="true"]

<div class="row">
    <div class="col-md-6">
        <h3>Recent News</h3>
        [dakoii_news district_id="123" limit="5"]
    </div>
    <div class="col-md-6">
        <h3>Upcoming Events</h3>
        [dakoii_events district_id="123" limit="5" upcoming_only="true"]
    </div>
</div>
```

### Creating a Sectors Overview Page
```html
<h1>Government Sectors</h1>
<p>Explore the various sectors of our provincial government and their leadership.</p>

[dakoii_sectors layout="grid" show_leadership="true" show_statement="true"]
```

### Creating an Individual Sector Page
```html
[dakoii_sector id="123" show_leadership="true" show_structure="true" show_statement="true"]

<h2>Contact This Sector</h2>
[dakoii_contact sector_id="123"]
```

### Creating the ESP Profile Page
```html
<h1>East Sepik Province Profile</h1>

<h2>Provincial Leadership</h2>
[dakoii_governor show_message="true"]

<h2>Provincial Administrator</h2>
[dakoii_administrators type="provincial" show_message="true"]

<h2>Our Members of Parliament</h2>
[dakoii_mps layout="list" show_all="true"]

<h2>Administrative Structure</h2>
[dakoii_administrators type="deputy" layout="grid"]
[dakoii_administrators type="executive" layout="grid"]

<h2>District Administrators</h2>
[dakoii_administrators type="district" layout="list"]

<h2>Provincial Overview</h2>
[dakoii_provincial_profile show_writeup="true" show_structure="true" show_map="true"]

<h2>Provincial Statistics</h2>
[dakoii_statistics layout="cards"]

<h2>Contact Information</h2>
[dakoii_contact type="provincial"]
```

---

## Required Features by Page Type

### Home Page Features ✅
- ✅ Special Banner (conditional display)
- ✅ Image with texts Slideshow in Hero
- ✅ Governor's Leadership Message (political)
- ✅ MPs List
- ✅ Featured news
- ✅ Featured Events

### Sectors Page Features ✅
- ✅ Sector Leadership and Message (Administration)
- ✅ Sector Functional Statement
- ✅ Sector Structure (Official Structure)

### Districts Page Features ✅
- ✅ District Hero (slideshow)
- ✅ District Leadership and Message (Political and Administration)
- ✅ District Statement
- ✅ District Structure
- ✅ District Map
- ✅ District Profile Information (item, description, numbers, images)
- ✅ District News
- ✅ District Events

### Events Page Features ✅
- ✅ Banner/Flyer Image
- ✅ Date and Time (Start date time - End Date Time)
- ✅ Venue
- ✅ Description
- ✅ Title
- ✅ Images

### Contacts Page Features ✅
- ✅ For Province and District

### ESP Profile Page Features ✅
- ✅ Provincial Leadership and Message (Political and Administration)
- ✅ Governor and Provincial Administrator
- ✅ MPs Display list all
- ✅ Provincial Administrator and Message
- ✅ Deputy Provincial Administrators list all
- ✅ Executive Managers list all
- ✅ District Administrators list all
- ✅ Provincial Write up
- ✅ Provincial Structure
- ✅ Provincial Map
- ✅ Province Profile Information - info groupings (item, description, numbers, images)

---

## File Structure for Implementation

### New Files to Create:
```
dev_guide/
├── website-features-integration-guide.md (this file)

includes/
├── class-provincial-sectors.php (new)
├── class-provincial-banners.php (new)
├── class-provincial-administrators.php (new)

admin/views/
├── sectors.php (new)
├── banners.php (new)
├── administrators.php (new)

templates/ (new directory)
├── single-esp_sector.php
├── single-esp_banner.php
├── archive-esp_sector.php

public/css/
├── enhanced-styles.css (new)
```

### Files to Modify:
```
includes/
├── class-provincial-post-types.php (extend)
├── class-provincial-meta-boxes.php (extend)
├── class-provincial-shortcodes.php (extend)
├── class-provincial-frontend.php (extend)
├── class-provincial-admin.php (extend)

public/css/
├── public-style.css (extend)

dakoii-provincial-admin-manager.php (extend)
```

---

## Conclusion

This implementation strategy provides:

1. **Smooth Integration** - Builds on existing plugin architecture
2. **Backward Compatibility** - Maintains all current functionality
3. **Flexibility** - Shortcode system allows for varied layouts
4. **Scalability** - Easy to add new features in the future
5. **WordPress Standards** - Follows WordPress best practices
6. **Theme Compatibility** - Works with any WordPress theme

The modular approach allows you to implement features incrementally, testing each component before moving to the next. Each shortcode works independently, so you can build pages gradually and customize the display to match your specific needs.

### Next Steps:
1. Start with Phase 1 implementation
2. Test each feature as you build it
3. Gather user feedback on admin interfaces
4. Refine styling and responsive behavior
5. Add advanced features based on usage patterns

This guide provides a solid foundation for creating a comprehensive provincial administration website while maintaining the flexibility and reliability of WordPress.

### Support and Maintenance:
- All new features follow existing plugin patterns
- Comprehensive error handling and validation
- Responsive design for all devices
- SEO-friendly markup and structure
- Accessibility considerations built-in
- Performance optimized with caching support

The implementation maintains the plugin's existing PNG color scheme and design patterns while adding powerful new functionality for comprehensive provincial administration management.
