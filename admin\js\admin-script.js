/**
 * Provincial Administration Manager - Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize admin functionality
    $(document).ready(function() {
        initTabs();
        initMediaUpload();
        initFormValidation();
        initTooltips();
        initConfirmDialogs();
        initAutoSave();
        initSearchFilters();
    });

    // Tab functionality
    function initTabs() {
        $('.esp-tab-nav button').on('click', function() {
            const tabId = $(this).data('tab');
            
            // Update nav
            $('.esp-tab-nav button').removeClass('active');
            $(this).addClass('active');
            
            // Update content
            $('.esp-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        });
    }

    // Media upload functionality
    function initMediaUpload() {
        $('.esp-upload-photo').on('click', function(e) {
            e.preventDefault();

            // Check if wp.media is available
            if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
                alert('Media uploader not available. Please refresh the page.');
                return;
            }

            const button = $(this);
            const targetInput = button.data('target');

            const mediaUploader = wp.media({
                title: 'Select Governor Photo',
                button: {
                    text: 'Use this photo'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });

            mediaUploader.on('select', function() {
                const attachment = mediaUploader.state().get('selection').first().toJSON();

                if (targetInput) {
                    $('#' + targetInput).val(attachment.id);

                    // Update preview immediately
                    const formRow = button.closest('.esp-form-row');
                    const existingPreview = formRow.find('.esp-media-preview');
                    const uploadArea = formRow.find('.esp-media-upload');

                    // Create new preview HTML
                    const newPreviewHtml = '<div class="esp-media-preview"><img src="' + attachment.url + '" style="max-width: 300px; height: auto; border-radius: 8px; object-fit: cover;" alt="Governor Photo"><p><button type="button" class="button esp-upload-photo" data-target="' + targetInput + '">Change Photo</button><input type="hidden" id="' + targetInput + '" name="governor_photo" value="' + attachment.id + '"><div style="font-size: 11px; color: #666; margin-top: 5px;">Photo ID: ' + attachment.id + '</div></p></div>';

                    if (existingPreview.length > 0) {
                        existingPreview.replaceWith(newPreviewHtml);
                    } else if (uploadArea.length > 0) {
                        uploadArea.replaceWith(newPreviewHtml);
                    }

                    // Re-initialize upload functionality for new button
                    initMediaUpload();

                    // Show success message
                    showNotification('Photo selected successfully. Don\'t forget to save the form!', 'success');
                }
            });

            mediaUploader.open();
        });
    }

    // Form validation
    function initFormValidation() {
        $('form').on('submit', function(e) {
            let isValid = true;
            
            // Check required fields
            $(this).find('[required]').each(function() {
                if (!$(this).val().trim()) {
                    isValid = false;
                    $(this).addClass('error');
                    showFieldError($(this), 'This field is required');
                } else {
                    $(this).removeClass('error');
                    hideFieldError($(this));
                }
            });
            
            // Check email fields
            $(this).find('input[type="email"]').each(function() {
                const email = $(this).val();
                if (email && !isValidEmail(email)) {
                    isValid = false;
                    $(this).addClass('error');
                    showFieldError($(this), 'Please enter a valid email address');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('Please correct the errors below', 'error');
            }
        });
        
        // Real-time validation
        $('input, textarea').on('blur', function() {
            validateField($(this));
        });
    }

    // Field validation helper
    function validateField(field) {
        const value = field.val().trim();
        
        if (field.prop('required') && !value) {
            field.addClass('error');
            showFieldError(field, 'This field is required');
            return false;
        }
        
        if (field.attr('type') === 'email' && value && !isValidEmail(value)) {
            field.addClass('error');
            showFieldError(field, 'Please enter a valid email address');
            return false;
        }
        
        field.removeClass('error');
        hideFieldError(field);
        return true;
    }

    // Email validation
    function isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }

    // Show field error
    function showFieldError(field, message) {
        hideFieldError(field);
        field.after('<div class="esp-field-error" style="color: #dc3232; font-size: 12px; margin-top: 5px;">' + message + '</div>');
    }

    // Hide field error
    function hideFieldError(field) {
        field.next('.esp-field-error').remove();
    }

    // Tooltips
    function initTooltips() {
        $('[data-tooltip]').hover(
            function() {
                const tooltip = $('<div class="esp-tooltip">' + $(this).data('tooltip') + '</div>');
                $('body').append(tooltip);
                
                const pos = $(this).offset();
                tooltip.css({
                    top: pos.top - tooltip.outerHeight() - 10,
                    left: pos.left + ($(this).outerWidth() / 2) - (tooltip.outerWidth() / 2)
                });
            },
            function() {
                $('.esp-tooltip').remove();
            }
        );
    }

    // Confirm dialogs
    function initConfirmDialogs() {
        $('.esp-delete-button, .delete').on('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
        
        $('.esp-bulk-delete').on('click', function(e) {
            const checkedItems = $('input[name="bulk-delete[]"]:checked').length;
            if (checkedItems === 0) {
                e.preventDefault();
                alert('Please select items to delete.');
                return;
            }
            
            if (!confirm('Are you sure you want to delete ' + checkedItems + ' item(s)? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    }

    // Auto-save functionality
    function initAutoSave() {
        let autoSaveTimer;
        
        $('input, textarea').on('input', function() {
            clearTimeout(autoSaveTimer);
            
            // Show unsaved changes indicator
            $('.esp-unsaved-indicator').remove();
            $('.submit').prepend('<span class="esp-unsaved-indicator" style="color: #d63638; margin-right: 10px;">Unsaved changes</span>');
            
            // Auto-save after 5 seconds of inactivity
            autoSaveTimer = setTimeout(function() {
                // Implement auto-save logic here if needed
                console.log('Auto-save triggered');
            }, 5000);
        });
        
        $('form').on('submit', function() {
            $('.esp-unsaved-indicator').remove();
        });
    }

    // Search and filter functionality
    function initSearchFilters() {
        // Search in tables
        $('.esp-search-input').on('keyup', function() {
            const value = $(this).val().toLowerCase();
            const table = $(this).closest('.esp-admin-section').find('table tbody tr');
            
            table.filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
        
        // Filter dropdowns
        $('.esp-filter-select').on('change', function() {
            const value = $(this).val();
            const filterType = $(this).data('filter');
            const table = $(this).closest('.esp-admin-section').find('table tbody tr');
            
            if (value === '') {
                table.show();
            } else {
                table.each(function() {
                    const cellValue = $(this).find('[data-' + filterType + ']').data(filterType);
                    $(this).toggle(cellValue === value);
                });
            }
        });
    }

    // Notification system
    function showNotification(message, type = 'success') {
        const notification = $('<div class="esp-notification esp-notification-' + type + '">' + message + '</div>');
        
        $('body').append(notification);
        
        notification.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '4px',
            zIndex: 9999,
            maxWidth: '300px'
        });
        
        if (type === 'success') {
            notification.css({
                background: '#d4edda',
                border: '1px solid #c3e6cb',
                color: '#155724'
            });
        } else if (type === 'error') {
            notification.css({
                background: '#f8d7da',
                border: '1px solid #f5c6cb',
                color: '#721c24'
            });
        }
        
        setTimeout(function() {
            notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Bulk actions
    function initBulkActions() {
        $('#bulk-action-selector-top, #bulk-action-selector-bottom').on('change', function() {
            const action = $(this).val();
            const otherSelector = $(this).attr('id') === 'bulk-action-selector-top' ? 
                '#bulk-action-selector-bottom' : '#bulk-action-selector-top';
            
            $(otherSelector).val(action);
        });
        
        $('.check-all').on('change', function() {
            const isChecked = $(this).prop('checked');
            $('input[name="bulk-delete[]"]').prop('checked', isChecked);
        });
    }

    // Data export functionality
    function initDataExport() {
        $('.esp-export-button').on('click', function() {
            const dataType = $(this).data('type');
            const format = $(this).data('format') || 'csv';
            
            // Show loading
            $(this).prop('disabled', true).text('Exporting...');
            
            // Simulate export (replace with actual AJAX call)
            setTimeout(() => {
                $(this).prop('disabled', false).text('Export ' + format.toUpperCase());
                showNotification('Export completed successfully');
            }, 2000);
        });
    }

    // Initialize additional features
    initBulkActions();
    initDataExport();

    // Global utility functions
    window.espAdmin = {
        showNotification: showNotification,
        validateField: validateField,
        isValidEmail: isValidEmail
    };

})(jQuery);
