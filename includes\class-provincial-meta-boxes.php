<?php
/**
 * Provincial Administration Meta Boxes Class
 * 
 * Handles meta boxes for custom post types
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_Meta_Boxes {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        // Governor meta boxes
        add_meta_box(
            'provincial_governor_details',
            __('Governor Details', 'esp-admin-manager'),
            array($this, 'governor_meta_box_callback'),
            'esp_governor',
            'normal',
            'high'
        );
        
        // MP meta boxes
        add_meta_box(
            'provincial_mp_details',
            __('MP Details', 'esp-admin-manager'),
            array($this, 'mp_meta_box_callback'),
            'esp_mp',
            'normal',
            'high'
        );
        
        // District meta boxes
        add_meta_box(
            'provincial_district_details',
            __('District Details', 'esp-admin-manager'),
            array($this, 'district_meta_box_callback'),
            'esp_district',
            'normal',
            'high'
        );

        // Contact meta boxes
        add_meta_box(
            'provincial_contact_details',
            __('Contact Details', 'esp-admin-manager'),
            array($this, 'contact_meta_box_callback'),
            'esp_contact',
            'normal',
            'high'
        );

        // Event meta boxes
        add_meta_box(
            'provincial_event_details',
            __('Event Details', 'esp-admin-manager'),
            array($this, 'event_meta_box_callback'),
            'esp_event',
            'normal',
            'high'
        );
        
        // News meta boxes
        add_meta_box(
            'provincial_news_details',
            __('News Details', 'esp-admin-manager'),
            array($this, 'news_meta_box_callback'),
            'esp_news',
            'normal',
            'high'
        );
    }
    
    /**
     * Governor meta box callback
     */
    public function governor_meta_box_callback($post) {
        wp_nonce_field('provincial_governor_meta_box', 'provincial_governor_meta_box_nonce');
        
        $title = get_post_meta($post->ID, '_esp_governor_title', true);
        $party = get_post_meta($post->ID, '_esp_governor_party', true);
        $email = get_post_meta($post->ID, '_esp_governor_email', true);
        $phone = get_post_meta($post->ID, '_esp_governor_phone', true);
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="esp_governor_title"><?php _e('Official Title', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_governor_title" name="esp_governor_title" value="<?php echo esc_attr($title); ?>" class="regular-text" />
                    <p class="description"><?php _e('e.g., Governor of the Province', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_governor_party"><?php _e('Political Party', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_governor_party" name="esp_governor_party" value="<?php echo esc_attr($party); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_governor_email"><?php _e('Email Address', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="email" id="esp_governor_email" name="esp_governor_email" value="<?php echo esc_attr($email); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_governor_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_governor_phone" name="esp_governor_phone" value="<?php echo esc_attr($phone); ?>" class="regular-text" />
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * MP meta box callback
     */
    public function mp_meta_box_callback($post) {
        wp_nonce_field('provincial_mp_meta_box', 'provincial_mp_meta_box_nonce');
        
        $electorate = get_post_meta($post->ID, '_esp_mp_electorate', true);
        $party = get_post_meta($post->ID, '_esp_mp_party', true);
        $email = get_post_meta($post->ID, '_esp_mp_email', true);
        $phone = get_post_meta($post->ID, '_esp_mp_phone', true);
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="esp_mp_electorate"><?php _e('Electorate', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_mp_electorate" name="esp_mp_electorate" value="<?php echo esc_attr($electorate); ?>" class="regular-text" />
                    <p class="description"><?php _e('e.g., Wewak Open, Provincial', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_mp_party"><?php _e('Political Party', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_mp_party" name="esp_mp_party" value="<?php echo esc_attr($party); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_mp_email"><?php _e('Email Address', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="email" id="esp_mp_email" name="esp_mp_email" value="<?php echo esc_attr($email); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_mp_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_mp_phone" name="esp_mp_phone" value="<?php echo esc_attr($phone); ?>" class="regular-text" />
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * District meta box callback
     */
    public function district_meta_box_callback($post) {
        wp_nonce_field('provincial_district_meta_box', 'provincial_district_meta_box_nonce');

        // Basic district information
        $llgs = get_post_meta($post->ID, '_esp_district_llgs', true);
        $wards = get_post_meta($post->ID, '_esp_district_wards', true);
        $population = get_post_meta($post->ID, '_esp_district_population', true);
        $area = get_post_meta($post->ID, '_esp_district_area', true);

        // District contact information
        $contact_address = get_post_meta($post->ID, '_esp_district_contact_address', true);
        $contact_phone = get_post_meta($post->ID, '_esp_district_contact_phone', true);
        $contact_fax = get_post_meta($post->ID, '_esp_district_contact_fax', true);
        $contact_emergency = get_post_meta($post->ID, '_esp_district_contact_emergency', true);
        $contact_email = get_post_meta($post->ID, '_esp_district_contact_email', true);
        $contact_admin_email = get_post_meta($post->ID, '_esp_district_contact_admin_email', true);
        $contact_website = get_post_meta($post->ID, '_esp_district_contact_website', true);
        $contact_office_hours = get_post_meta($post->ID, '_esp_district_contact_office_hours', true);
        ?>
        <div style="margin-bottom: 20px;">
            <h3><?php _e('Basic Information', 'esp-admin-manager'); ?></h3>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="esp_district_llgs"><?php _e('Number of LLGs', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="esp_district_llgs" name="esp_district_llgs" value="<?php echo esc_attr($llgs); ?>" class="small-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_wards"><?php _e('Number of Wards', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="esp_district_wards" name="esp_district_wards" value="<?php echo esc_attr($wards); ?>" class="small-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_population"><?php _e('Population', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_population" name="esp_district_population" value="<?php echo esc_attr($population); ?>" class="regular-text" />
                        <p class="description"><?php _e('Optional: District population if available', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_area"><?php _e('Area (km²)', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_area" name="esp_district_area" value="<?php echo esc_attr($area); ?>" class="regular-text" />
                        <p class="description"><?php _e('Optional: District area if available', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <div style="margin-bottom: 20px;">
            <h3><?php _e('District Contact Information', 'esp-admin-manager'); ?></h3>
            <p class="description" style="margin-bottom: 15px;">
                <?php _e('Contact details specific to this district. These will be displayed using shortcodes like [dakoii_contact district_id="' . $post->ID . '"] or [dakoii_district_contact id="' . $post->ID . '"]', 'esp-admin-manager'); ?>
            </p>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_address"><?php _e('District Office Address', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="esp_district_contact_address" name="esp_district_contact_address" rows="4" class="large-text"><?php echo esc_textarea($contact_address); ?></textarea>
                        <p class="description"><?php _e('Complete physical address of the district office', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_contact_phone" name="esp_district_contact_phone" value="<?php echo esc_attr($contact_phone); ?>" class="regular-text" />
                        <p class="description"><?php _e('Main district office phone number', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_fax"><?php _e('Fax Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_contact_fax" name="esp_district_contact_fax" value="<?php echo esc_attr($contact_fax); ?>" class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_emergency"><?php _e('Emergency Contact', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_contact_emergency" name="esp_district_contact_emergency" value="<?php echo esc_attr($contact_emergency); ?>" class="regular-text" />
                        <p class="description"><?php _e('Emergency contact number for the district', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_email"><?php _e('General Email', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="esp_district_contact_email" name="esp_district_contact_email" value="<?php echo esc_attr($contact_email); ?>" class="regular-text" />
                        <p class="description"><?php _e('General inquiries email address', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_admin_email"><?php _e('Administrative Email', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="esp_district_contact_admin_email" name="esp_district_contact_admin_email" value="<?php echo esc_attr($contact_admin_email); ?>" class="regular-text" />
                        <p class="description"><?php _e('Administrative/official business email', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_website"><?php _e('Website', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="url" id="esp_district_contact_website" name="esp_district_contact_website" value="<?php echo esc_attr($contact_website); ?>" class="regular-text" />
                        <p class="description"><?php _e('District website URL (if available)', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_office_hours"><?php _e('Office Hours', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="esp_district_contact_office_hours" name="esp_district_contact_office_hours" rows="3" class="large-text"><?php echo esc_textarea($contact_office_hours); ?></textarea>
                        <p class="description"><?php _e('District office operating hours', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>
        <?php
    }

    /**
     * Contact meta box callback
     */
    public function contact_meta_box_callback($post) {
        wp_nonce_field('provincial_contact_meta_box', 'provincial_contact_meta_box_nonce');

        // Get contact information
        $address = get_post_meta($post->ID, '_esp_contact_address', true);
        $phone = get_post_meta($post->ID, '_esp_contact_phone', true);
        $fax = get_post_meta($post->ID, '_esp_contact_fax', true);
        $emergency = get_post_meta($post->ID, '_esp_contact_emergency', true);
        $email = get_post_meta($post->ID, '_esp_contact_email', true);
        $admin_email = get_post_meta($post->ID, '_esp_contact_admin_email', true);
        $website = get_post_meta($post->ID, '_esp_contact_website', true);
        $office_hours = get_post_meta($post->ID, '_esp_contact_office_hours', true);
        ?>
        <div style="margin-bottom: 20px;">
            <p class="description" style="margin-bottom: 15px;">
                <?php _e('Contact information for this contact. Use shortcode: [dakoii_contact id="' . $post->ID . '"] to display this contact.', 'esp-admin-manager'); ?>
            </p>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="esp_contact_address"><?php _e('Address', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="esp_contact_address" name="esp_contact_address" rows="4" class="large-text"><?php echo esc_textarea($address); ?></textarea>
                        <p class="description"><?php _e('Complete physical address', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_contact_phone" name="esp_contact_phone" value="<?php echo esc_attr($phone); ?>" class="regular-text" />
                        <p class="description"><?php _e('Main contact phone number', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_fax"><?php _e('Fax Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_contact_fax" name="esp_contact_fax" value="<?php echo esc_attr($fax); ?>" class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_emergency"><?php _e('Emergency Contact', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_contact_emergency" name="esp_contact_emergency" value="<?php echo esc_attr($emergency); ?>" class="regular-text" />
                        <p class="description"><?php _e('Emergency contact number', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_email"><?php _e('Email Address', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="esp_contact_email" name="esp_contact_email" value="<?php echo esc_attr($email); ?>" class="regular-text" />
                        <p class="description"><?php _e('General inquiries email address', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_admin_email"><?php _e('Administrative Email', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="esp_contact_admin_email" name="esp_contact_admin_email" value="<?php echo esc_attr($admin_email); ?>" class="regular-text" />
                        <p class="description"><?php _e('Administrative/official business email', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_website"><?php _e('Website', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="url" id="esp_contact_website" name="esp_contact_website" value="<?php echo esc_attr($website); ?>" class="regular-text" />
                        <p class="description"><?php _e('Website URL (if available)', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_office_hours"><?php _e('Office Hours', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="esp_contact_office_hours" name="esp_contact_office_hours" rows="3" class="large-text"><?php echo esc_textarea($office_hours); ?></textarea>
                        <p class="description"><?php _e('Office operating hours', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>
        <?php
    }

    /**
     * Event meta box callback
     */
    public function event_meta_box_callback($post) {
        wp_nonce_field('provincial_event_meta_box', 'provincial_event_meta_box_nonce');
        
        $start_date = get_post_meta($post->ID, '_esp_event_start_date', true);
        $end_date = get_post_meta($post->ID, '_esp_event_end_date', true);
        $location = get_post_meta($post->ID, '_esp_event_location', true);
        $contact = get_post_meta($post->ID, '_esp_event_contact', true);
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="esp_event_start_date"><?php _e('Start Date', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="date" id="esp_event_start_date" name="esp_event_start_date" value="<?php echo esc_attr($start_date); ?>" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_event_end_date"><?php _e('End Date', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="date" id="esp_event_end_date" name="esp_event_end_date" value="<?php echo esc_attr($end_date); ?>" />
                    <p class="description"><?php _e('Leave empty for single-day events', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_event_location"><?php _e('Location', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_event_location" name="esp_event_location" value="<?php echo esc_attr($location); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_event_contact"><?php _e('Contact Information', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <textarea id="esp_event_contact" name="esp_event_contact" rows="3" class="large-text"><?php echo esc_textarea($contact); ?></textarea>
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * News meta box callback
     */
    public function news_meta_box_callback($post) {
        wp_nonce_field('provincial_news_meta_box', 'provincial_news_meta_box_nonce');
        
        $news_date = get_post_meta($post->ID, '_esp_news_date', true);
        $source = get_post_meta($post->ID, '_esp_news_source', true);
        $featured = get_post_meta($post->ID, '_esp_news_featured', true);
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="esp_news_date"><?php _e('News Date', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="date" id="esp_news_date" name="esp_news_date" value="<?php echo esc_attr($news_date); ?>" />
                    <p class="description"><?php _e('Date when the news occurred (defaults to publish date)', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_news_source"><?php _e('Source', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_news_source" name="esp_news_source" value="<?php echo esc_attr($source); ?>" class="regular-text" />
                    <p class="description"><?php _e('e.g., Provincial Administration, Department of Health', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_news_featured"><?php _e('Featured News', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="checkbox" id="esp_news_featured" name="esp_news_featured" value="1" <?php checked($featured, '1'); ?> />
                    <label for="esp_news_featured"><?php _e('Mark as featured news', 'esp-admin-manager'); ?></label>
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * Save meta boxes
     */
    public function save_meta_boxes($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['provincial_governor_meta_box_nonce']) &&
            !isset($_POST['provincial_mp_meta_box_nonce']) &&
            !isset($_POST['provincial_district_meta_box_nonce']) &&
            !isset($_POST['provincial_contact_meta_box_nonce']) &&
            !isset($_POST['provincial_event_meta_box_nonce']) &&
            !isset($_POST['provincial_news_meta_box_nonce'])) {
            return;
        }
        
        // Check if user has permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Check if not an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        $post_type = get_post_type($post_id);
        
        // Save governor meta
        if ($post_type === 'esp_governor' && isset($_POST['provincial_governor_meta_box_nonce']) && 
            wp_verify_nonce($_POST['provincial_governor_meta_box_nonce'], 'provincial_governor_meta_box')) {
            
            update_post_meta($post_id, '_esp_governor_title', sanitize_text_field($_POST['esp_governor_title']));
            update_post_meta($post_id, '_esp_governor_party', sanitize_text_field($_POST['esp_governor_party']));
            update_post_meta($post_id, '_esp_governor_email', sanitize_email($_POST['esp_governor_email']));
            update_post_meta($post_id, '_esp_governor_phone', sanitize_text_field($_POST['esp_governor_phone']));
        }
        
        // Save MP meta
        if ($post_type === 'esp_mp' && isset($_POST['provincial_mp_meta_box_nonce']) && 
            wp_verify_nonce($_POST['provincial_mp_meta_box_nonce'], 'provincial_mp_meta_box')) {
            
            update_post_meta($post_id, '_esp_mp_electorate', sanitize_text_field($_POST['esp_mp_electorate']));
            update_post_meta($post_id, '_esp_mp_party', sanitize_text_field($_POST['esp_mp_party']));
            update_post_meta($post_id, '_esp_mp_email', sanitize_email($_POST['esp_mp_email']));
            update_post_meta($post_id, '_esp_mp_phone', sanitize_text_field($_POST['esp_mp_phone']));
        }
        
        // Save district meta
        if ($post_type === 'esp_district' && isset($_POST['provincial_district_meta_box_nonce']) &&
            wp_verify_nonce($_POST['provincial_district_meta_box_nonce'], 'provincial_district_meta_box')) {

            // Save basic district information
            update_post_meta($post_id, '_esp_district_llgs', sanitize_text_field($_POST['esp_district_llgs']));
            update_post_meta($post_id, '_esp_district_wards', sanitize_text_field($_POST['esp_district_wards']));
            update_post_meta($post_id, '_esp_district_population', sanitize_text_field($_POST['esp_district_population']));
            update_post_meta($post_id, '_esp_district_area', sanitize_text_field($_POST['esp_district_area']));

            // Save district contact information
            update_post_meta($post_id, '_esp_district_contact_address', sanitize_textarea_field($_POST['esp_district_contact_address']));
            update_post_meta($post_id, '_esp_district_contact_phone', sanitize_text_field($_POST['esp_district_contact_phone']));
            update_post_meta($post_id, '_esp_district_contact_fax', sanitize_text_field($_POST['esp_district_contact_fax']));
            update_post_meta($post_id, '_esp_district_contact_emergency', sanitize_text_field($_POST['esp_district_contact_emergency']));
            update_post_meta($post_id, '_esp_district_contact_email', sanitize_email($_POST['esp_district_contact_email']));
            update_post_meta($post_id, '_esp_district_contact_admin_email', sanitize_email($_POST['esp_district_contact_admin_email']));
            update_post_meta($post_id, '_esp_district_contact_website', sanitize_url($_POST['esp_district_contact_website']));
            update_post_meta($post_id, '_esp_district_contact_office_hours', sanitize_textarea_field($_POST['esp_district_contact_office_hours']));
        }

        // Save contact meta
        if ($post_type === 'esp_contact' && isset($_POST['provincial_contact_meta_box_nonce']) &&
            wp_verify_nonce($_POST['provincial_contact_meta_box_nonce'], 'provincial_contact_meta_box')) {

            update_post_meta($post_id, '_esp_contact_address', sanitize_textarea_field($_POST['esp_contact_address']));
            update_post_meta($post_id, '_esp_contact_phone', sanitize_text_field($_POST['esp_contact_phone']));
            update_post_meta($post_id, '_esp_contact_fax', sanitize_text_field($_POST['esp_contact_fax']));
            update_post_meta($post_id, '_esp_contact_emergency', sanitize_text_field($_POST['esp_contact_emergency']));
            update_post_meta($post_id, '_esp_contact_email', sanitize_email($_POST['esp_contact_email']));
            update_post_meta($post_id, '_esp_contact_admin_email', sanitize_email($_POST['esp_contact_admin_email']));
            update_post_meta($post_id, '_esp_contact_website', sanitize_url($_POST['esp_contact_website']));
            update_post_meta($post_id, '_esp_contact_office_hours', sanitize_textarea_field($_POST['esp_contact_office_hours']));
        }

        // Save event meta
        if ($post_type === 'esp_event' && isset($_POST['provincial_event_meta_box_nonce']) && 
            wp_verify_nonce($_POST['provincial_event_meta_box_nonce'], 'provincial_event_meta_box')) {
            
            update_post_meta($post_id, '_esp_event_start_date', sanitize_text_field($_POST['esp_event_start_date']));
            update_post_meta($post_id, '_esp_event_end_date', sanitize_text_field($_POST['esp_event_end_date']));
            update_post_meta($post_id, '_esp_event_location', sanitize_text_field($_POST['esp_event_location']));
            update_post_meta($post_id, '_esp_event_contact', sanitize_textarea_field($_POST['esp_event_contact']));
        }
        
        // Save news meta
        if ($post_type === 'esp_news' && isset($_POST['provincial_news_meta_box_nonce']) && 
            wp_verify_nonce($_POST['provincial_news_meta_box_nonce'], 'provincial_news_meta_box')) {
            
            update_post_meta($post_id, '_esp_news_date', sanitize_text_field($_POST['esp_news_date']));
            update_post_meta($post_id, '_esp_news_source', sanitize_text_field($_POST['esp_news_source']));
            update_post_meta($post_id, '_esp_news_featured', isset($_POST['esp_news_featured']) ? '1' : '0');
        }
    }
}
