<?php
/**
 * Provincial Administration Manager - Contact Information View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current contact information
$contact = get_option('esp_contact_information', array());
$multiple_contacts = get_option('esp_multiple_contacts', array());

// Handle edit mode for multiple contacts
$editing_contact = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['contact_id'])) {
    $contact_id = sanitize_text_field($_GET['contact_id']);
    if (isset($multiple_contacts[$contact_id])) {
        $editing_contact = $multiple_contacts[$contact_id];
    }
}
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Contact Information Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage contact details for the Provincial Administration', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <form method="post" action="">
        <?php wp_nonce_field('provincial_contact_nonce', 'provincial_nonce'); ?>
        
        <div class="esp-form-section">
            <h3><?php _e('Provincial Headquarters', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="address"><?php _e('Physical Address', 'esp-admin-manager'); ?></label>
                <div>
                    <textarea id="address" name="address" rows="4" class="large-text"><?php echo esc_textarea($contact['address'] ?? 'Provincial Headquarters, Papua New Guinea, PO Box 280'); ?></textarea>
                    <div class="description"><?php _e('Complete physical address including postal box if applicable', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Phone & Fax Numbers', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="phone"><?php _e('Main Phone Number', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="phone" name="phone" 
                           value="<?php echo esc_attr($contact['phone'] ?? '(+*************'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Main switchboard or reception number', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="fax"><?php _e('Fax Number', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="fax" name="fax" 
                           value="<?php echo esc_attr($contact['fax'] ?? '(+*************'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Official fax number for documents', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="emergency"><?php _e('Emergency Number', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="emergency" name="emergency" 
                           value="<?php echo esc_attr($contact['emergency'] ?? '000'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Emergency services contact number', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Email & Web', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="email"><?php _e('General Email', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="email" id="email" name="email" 
                           value="<?php echo esc_attr($contact['email'] ?? '<EMAIL>'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('General inquiries email address', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="admin_email"><?php _e('Administration Email', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="email" id="admin_email" name="admin_email" 
                           value="<?php echo esc_attr($contact['admin_email'] ?? '<EMAIL>'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Administrative matters email address', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="website"><?php _e('Official Website', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="website" name="website" 
                           value="<?php echo esc_attr($contact['website'] ?? 'www.eastsepik.gov.pg'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Official website URL (without http://)', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Office Hours', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="office_hours"><?php _e('Operating Hours', 'esp-admin-manager'); ?></label>
                <div>
                    <textarea id="office_hours" name="office_hours" rows="3" class="large-text"><?php echo esc_textarea($contact['office_hours'] ?? 'Monday - Friday, 8:00 AM - 4:30 PM, Closed Weekends & Public Holidays'); ?></textarea>
                    <div class="description"><?php _e('When the provincial offices are open to the public', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <p class="submit">
            <input type="submit" name="submit" class="button-primary esp-button large" 
                   value="<?php _e('Save Contact Information', 'esp-admin-manager'); ?>" />
        </p>
    </form>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the contact information will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px;">
            <?php echo do_shortcode('[dakoii_prov_admin_contact]'); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong>
            <code>[dakoii_prov_admin_contact]</code> <?php _e('(Primary)', 'esp-admin-manager'); ?>
            <small style="color: #666; margin-left: 10px;"><?php _e('or', 'esp-admin-manager'); ?> <code>[dakoii_contact]</code>, <code>[esp_contact]</code></small>
        </p>
        <p>
            <?php _e('You can also use:', 'esp-admin-manager'); ?> <code>[dakoii_prov_admin_contact layout="list"]</code> 
            <?php _e('for a vertical list layout.', 'esp-admin-manager'); ?>
        </p>
    </div>

    <!-- Quick Contact Cards -->
    <div class="esp-form-section">
        <h3><?php _e('Quick Reference', 'esp-admin-manager'); ?></h3>
        <div class="esp-dashboard-cards">
            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">📞</span>
                <h3><?php _e('Main Phone', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 18px;"><?php echo esc_html($contact['phone'] ?? '(+*************'); ?></div>
                <div class="description"><?php _e('Primary contact number', 'esp-admin-manager'); ?></div>
            </div>

            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">✉️</span>
                <h3><?php _e('General Email', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 14px;"><?php echo esc_html($contact['email'] ?? '<EMAIL>'); ?></div>
                <div class="description"><?php _e('General inquiries', 'esp-admin-manager'); ?></div>
            </div>

            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">🚨</span>
                <h3><?php _e('Emergency', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 24px; color: #dc3232;"><?php echo esc_html($contact['emergency'] ?? '000'); ?></div>
                <div class="description"><?php _e('Emergency services', 'esp-admin-manager'); ?></div>
            </div>

            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">🌐</span>
                <h3><?php _e('Website', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 14px;"><?php echo esc_html($contact['website'] ?? 'www.eastsepik.gov.pg'); ?></div>
                <div class="description"><?php _e('Official website', 'esp-admin-manager'); ?></div>
            </div>
        </div>
    </div>

    <!-- Multiple Contacts Section -->
    <div class="esp-form-section" style="margin-top: 40px; border-top: 2px solid #ddd; padding-top: 30px;">
        <h2><?php _e('Multiple Contact Sets', 'esp-admin-manager'); ?></h2>
        <p><?php _e('Create multiple contact sets, each with its own shortcode. Perfect for different departments, offices, or services.', 'esp-admin-manager'); ?></p>

        <!-- Existing Multiple Contacts List -->
        <?php if (!empty($multiple_contacts)): ?>
            <div class="esp-contacts-list" style="margin-bottom: 20px;">
                <h3><?php _e('Existing Contacts', 'esp-admin-manager'); ?></h3>
                <table class="esp-list-table">
                    <thead>
                        <tr>
                            <th><?php _e('Contact Name', 'esp-admin-manager'); ?></th>
                            <th><?php _e('Phone', 'esp-admin-manager'); ?></th>
                            <th><?php _e('Email', 'esp-admin-manager'); ?></th>
                            <th><?php _e('Shortcode', 'esp-admin-manager'); ?></th>
                            <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($multiple_contacts as $contact_item): ?>
                            <tr>
                                <td><strong><?php echo esc_html($contact_item['name']); ?></strong></td>
                                <td><?php echo esc_html($contact_item['phone'] ?: '—'); ?></td>
                                <td><?php echo esc_html($contact_item['email'] ?: '—'); ?></td>
                                <td>
                                    <code>[dakoii_contact id="<?php echo esc_attr($contact_item['id']); ?>"]</code>
                                    <button type="button" class="button-link copy-shortcode" data-shortcode='[dakoii_contact id="<?php echo esc_attr($contact_item['id']); ?>"]'>
                                        <?php _e('Copy', 'esp-admin-manager'); ?>
                                    </button>
                                </td>
                                <td>
                                    <a href="?page=provincial-admin-contact&action=edit&contact_id=<?php echo esc_attr($contact_item['id']); ?>" class="button button-small">
                                        <?php _e('Edit', 'esp-admin-manager'); ?>
                                    </a>
                                    <form method="post" style="display: inline;" onsubmit="return confirm('<?php _e('Are you sure you want to delete this contact?', 'esp-admin-manager'); ?>');">
                                        <?php wp_nonce_field('multiple_contact_action', 'multiple_contact_nonce'); ?>
                                        <input type="hidden" name="action" value="delete_multiple_contact">
                                        <input type="hidden" name="contact_id" value="<?php echo esc_attr($contact_item['id']); ?>">
                                        <input type="submit" class="button button-small button-link-delete" value="<?php _e('Delete', 'esp-admin-manager'); ?>">
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <!-- Add/Edit Contact Form -->
        <?php if ($editing_contact || isset($_GET['action']) && $_GET['action'] === 'add'): ?>
            <div class="esp-contact-form" style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h3>
                    <?php if ($editing_contact): ?>
                        <?php _e('Edit Contact:', 'esp-admin-manager'); ?> <?php echo esc_html($editing_contact['name']); ?>
                    <?php else: ?>
                        <?php _e('Add New Contact', 'esp-admin-manager'); ?>
                    <?php endif; ?>
                </h3>

                <form method="post">
                    <?php wp_nonce_field('multiple_contact_action', 'multiple_contact_nonce'); ?>
                    <input type="hidden" name="action" value="<?php echo $editing_contact ? 'edit_multiple_contact' : 'add_multiple_contact'; ?>">
                    <?php if ($editing_contact): ?>
                        <input type="hidden" name="contact_id" value="<?php echo esc_attr($editing_contact['id']); ?>">
                    <?php endif; ?>

                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="contact_name"><?php _e('Contact Name', 'esp-admin-manager'); ?> *</label>
                            </th>
                            <td>
                                <input type="text" id="contact_name" name="contact_name" value="<?php echo esc_attr($editing_contact['name'] ?? ''); ?>" class="regular-text" required />
                                <p class="description"><?php _e('e.g., "Emergency Services", "Tourism Office", "Health Department"', 'esp-admin-manager'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="contact_address"><?php _e('Address', 'esp-admin-manager'); ?></label>
                            </th>
                            <td>
                                <textarea id="contact_address" name="contact_address" rows="4" class="large-text"><?php echo esc_textarea($editing_contact['address'] ?? ''); ?></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="contact_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="contact_phone" name="contact_phone" value="<?php echo esc_attr($editing_contact['phone'] ?? ''); ?>" class="regular-text" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="contact_email"><?php _e('Email Address', 'esp-admin-manager'); ?></label>
                            </th>
                            <td>
                                <input type="email" id="contact_email" name="contact_email" value="<?php echo esc_attr($editing_contact['email'] ?? ''); ?>" class="regular-text" />
                            </td>
                        </tr>
                    </table>

                    <p class="submit">
                        <input type="submit" class="button-primary esp-button" value="<?php echo $editing_contact ? __('Update Contact', 'esp-admin-manager') : __('Add Contact', 'esp-admin-manager'); ?>" />
                        <a href="?page=provincial-admin-contact" class="button"><?php _e('Cancel', 'esp-admin-manager'); ?></a>
                    </p>
                </form>
            </div>
        <?php else: ?>
            <div class="esp-add-contact" style="margin-bottom: 20px;">
                <a href="?page=provincial-admin-contact&action=add" class="button button-primary esp-button">
                    <?php _e('Add New Contact', 'esp-admin-manager'); ?>
                </a>
            </div>
        <?php endif; ?>

        <!-- Usage Instructions -->
        <div class="esp-usage-info" style="background: #e7f3ff; padding: 15px; border-radius: 8px; border-left: 4px solid #0073aa;">
            <h4><?php _e('How to Use Multiple Contacts', 'esp-admin-manager'); ?></h4>
            <p><?php _e('Each contact gets its own unique shortcode that you can use anywhere on your website:', 'esp-admin-manager'); ?></p>
            <ul style="margin: 10px 0 0 20px;">
                <li><strong><?php _e('Basic usage:', 'esp-admin-manager'); ?></strong> <code>[dakoii_contact id="contact_id"]</code></li>
                <li><strong><?php _e('With layout:', 'esp-admin-manager'); ?></strong> <code>[dakoii_contact id="contact_id" layout="list"]</code></li>
                <li><strong><?php _e('Alternative:', 'esp-admin-manager'); ?></strong> <code>[dakoii_provincial_contact id="contact_id"]</code></li>
            </ul>
            <p><strong><?php _e('Note:', 'esp-admin-manager'); ?></strong> <?php _e('The legacy shortcode [dakoii_contact] (without ID) will continue to show the Provincial Headquarters contact above.', 'esp-admin-manager'); ?></p>
        </div>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Contact Information Help', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Accurate contact information is essential for citizens to reach government services. Consider these best practices:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Keep all contact information current and verify regularly', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use international format for phone numbers: (+675) XXX-XXXX', 'esp-admin-manager'); ?></li>
            <li><?php _e('Provide multiple contact methods for accessibility', 'esp-admin-manager'); ?></li>
            <li><?php _e('Include clear office hours to manage citizen expectations', 'esp-admin-manager'); ?></li>
            <li><?php _e('Test email addresses and phone numbers periodically', 'esp-admin-manager'); ?></li>
            <li><?php _e('Consider providing department-specific contact information', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Format phone numbers as user types
    $('#phone, #fax').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.startsWith('675')) {
            value = '(+675) ' + value.substring(3);
        }
        // Add more formatting logic as needed
    });
    
    // Validate email addresses
    $('input[type="email"]').on('blur', function() {
        let email = $(this).val();
        let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            $(this).css('border-color', '#dc3232');
            if (!$(this).next('.error-message').length) {
                $(this).after('<div class="error-message" style="color: #dc3232; font-size: 12px; margin-top: 5px;">Please enter a valid email address</div>');
            }
        } else {
            $(this).css('border-color', '');
            $(this).next('.error-message').remove();
        }
    });
    
    // Website URL formatting
    $('#website, #contact_website').on('blur', function() {
        let url = $(this).val();
        if (url && !url.startsWith('www.') && !url.startsWith('http')) {
            $(this).val('www.' + url);
        }
    });

    // Copy shortcode functionality
    $('.copy-shortcode').on('click', function() {
        let shortcode = $(this).data('shortcode');
        navigator.clipboard.writeText(shortcode).then(function() {
            // Show success message
            let button = $(this);
            let originalText = button.text();
            button.text('Copied!').css('color', '#46b450');
            setTimeout(function() {
                button.text(originalText).css('color', '');
            }, 2000);
        }.bind(this));
    });

    // Format phone numbers for multiple contact fields
    $('#contact_phone, #contact_fax').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.startsWith('675')) {
            value = '(+675) ' + value.substring(3);
        }
        // Add more formatting logic as needed
    });

    // Validate email addresses for multiple contact fields
    $('#contact_email, #contact_admin_email').on('blur', function() {
        let email = $(this).val();
        let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && !emailRegex.test(email)) {
            $(this).css('border-color', '#dc3232');
            if (!$(this).next('.error-message').length) {
                $(this).after('<div class="error-message" style="color: #dc3232; font-size: 12px; margin-top: 5px;">Please enter a valid email address</div>');
            }
        } else {
            $(this).css('border-color', '');
            $(this).next('.error-message').remove();
        }
    });
});
</script>
