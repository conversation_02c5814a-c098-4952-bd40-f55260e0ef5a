<?php
/**
 * Provincial Administration Manager - Events Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get all events
$events = get_posts(array(
    'post_type' => 'esp_event',
    'numberposts' => -1,
    'post_status' => 'any',
    'orderby' => 'meta_value',
    'meta_key' => '_esp_event_start_date',
    'order' => 'ASC'
));
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Events Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage government events, meetings, and public activities', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <!-- Quick Actions -->
    <div class="esp-quick-actions">
        <h3><?php _e('Quick Actions', 'esp-admin-manager'); ?></h3>
        <div class="actions">
            <a href="<?php echo admin_url('post-new.php?post_type=esp_event'); ?>" class="esp-button">
                <?php _e('Add New Event', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('edit.php?post_type=esp_event'); ?>" class="esp-button secondary">
                <?php _e('Manage All Events', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- Events List -->
    <div class="esp-form-section">
        <h3><?php _e('Upcoming & Recent Events', 'esp-admin-manager'); ?></h3>
        
        <?php if (!empty($events)): ?>
        <table class="esp-list-table">
            <thead>
                <tr>
                    <th><?php _e('Event Title', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Date(s)', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Location', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Status', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($events as $event): 
                    $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                    $end_date = get_post_meta($event->ID, '_esp_event_end_date', true);
                    $location = get_post_meta($event->ID, '_esp_event_location', true);
                    
                    // Determine if event is upcoming, current, or past
                    $today = date('Y-m-d');
                    $event_status = 'past';
                    if ($start_date >= $today) {
                        $event_status = 'upcoming';
                    } elseif ($end_date && $end_date >= $today) {
                        $event_status = 'current';
                    }
                ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($event->post_title); ?></strong>
                        <div style="font-size: 12px; color: #666;">
                            <?php echo esc_html(wp_trim_words($event->post_content, 15)); ?>
                        </div>
                    </td>
                    <td>
                        <?php if ($start_date): ?>
                            <div style="font-weight: bold;">
                                <?php echo esc_html(date('M j, Y', strtotime($start_date))); ?>
                            </div>
                            <?php if ($end_date && $end_date !== $start_date): ?>
                                <div style="font-size: 12px; color: #666;">
                                    to <?php echo esc_html(date('M j, Y', strtotime($end_date))); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <span style="color: #999;">No date set</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($location): ?>
                            <span style="font-size: 14px;">
                                <?php echo esc_html($location); ?>
                            </span>
                        <?php else: ?>
                            <span style="color: #999;">—</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($event->post_status === 'publish'): ?>
                            <?php if ($event_status === 'upcoming'): ?>
                                <span style="color: blue;">●</span> <?php _e('Upcoming', 'esp-admin-manager'); ?>
                            <?php elseif ($event_status === 'current'): ?>
                                <span style="color: green;">●</span> <?php _e('Current', 'esp-admin-manager'); ?>
                            <?php else: ?>
                                <span style="color: #666;">●</span> <?php _e('Past', 'esp-admin-manager'); ?>
                            <?php endif; ?>
                        <?php else: ?>
                            <span style="color: orange;">●</span> <?php echo esc_html(ucfirst($event->post_status)); ?>
                        <?php endif; ?>
                    </td>
                    <td class="actions">
                        <a href="<?php echo get_edit_post_link($event->ID); ?>" class="edit">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </a>
                        <a href="<?php echo get_delete_post_link($event->ID); ?>" class="delete" 
                           onclick="return confirm('<?php _e('Are you sure you want to delete this event?', 'esp-admin-manager'); ?>')">
                            <?php _e('Delete', 'esp-admin-manager'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div class="esp-message warning">
            <p><?php _e('No events found. Click "Add New Event" to get started.', 'esp-admin-manager'); ?></p>
        </div>
        <?php endif; ?>
    </div>

    <!-- Event Statistics -->
    <div class="esp-form-section">
        <h3><?php _e('Event Statistics', 'esp-admin-manager'); ?></h3>
        <div class="esp-stats-grid">
            <div class="esp-stats-item">
                <label><?php _e('Total Events', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo count($events); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Upcoming Events', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: blue;">
                    <?php 
                    $upcoming = 0;
                    $today = date('Y-m-d');
                    foreach ($events as $event) {
                        $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                        if ($start_date >= $today && $event->post_status === 'publish') {
                            $upcoming++;
                        }
                    }
                    echo $upcoming;
                    ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Published', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: green;">
                    <?php echo count(array_filter($events, function($event) { return $event->post_status === 'publish'; })); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('This Month', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-primary);">
                    <?php 
                    $this_month = 0;
                    $current_month = date('Y-m');
                    foreach ($events as $event) {
                        $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                        if ($start_date && substr($start_date, 0, 7) === $current_month) {
                            $this_month++;
                        }
                    }
                    echo $this_month;
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the events will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px; max-height: 400px; overflow-y: auto;">
            <?php echo do_shortcode('[dakoii_events limit="5"]'); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong>
            <code>[dakoii_prov_admin_events]</code> <?php _e('(Primary)', 'esp-admin-manager'); ?>
            <small style="color: #666; margin-left: 10px;"><?php _e('or', 'esp-admin-manager'); ?> <code>[dakoii_events]</code>, <code>[esp_events]</code></small>
        </p>
        <p>
            <?php _e('Options:', 'esp-admin-manager'); ?>
            <code>[dakoii_prov_admin_events limit="5"]</code> - <?php _e('Show only 5 events', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_events upcoming_only="false"]</code> - <?php _e('Show all events (including past)', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_events show_images="false"]</code> - <?php _e('Hide featured images', 'esp-admin-manager'); ?>
        </p>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Managing Events', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Events help keep citizens informed about government activities and public meetings. Here are some best practices:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Add events well in advance to give citizens time to plan', 'esp-admin-manager'); ?></li>
            <li><?php _e('Include clear, specific location information', 'esp-admin-manager'); ?></li>
            <li><?php _e('Provide detailed descriptions of what the event involves', 'esp-admin-manager'); ?></li>
            <li><?php _e('Update or remove events if they are cancelled or postponed', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use the contact field for RSVP or inquiry information', 'esp-admin-manager'); ?></li>
            <li><?php _e('Consider adding photos after events for public engagement', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>
