<?php
/**
 * Provincial Administration Manager - Administrative Structure View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current administrative structure
$structure = get_option('esp_administrative_structure', array());

// Default structure data
$default_government_sectors = "
<ul>
<li>Health & Medical Services</li>
<li>Education & Training</li>
<li>Infrastructure & Transport</li>
<li>Agriculture & Livestock</li>
<li>Commerce & Industry</li>
<li>Tourism & Culture</li>
<li>Environment & Conservation</li>
<li>Community Development</li>
<li>Youth & Sports</li>
<li>Women & Family Affairs</li>
<li>Law & Justice</li>
<li>Finance & Treasury</li>
</ul>";

$default_administrative_divisions = "
<ul>
<li>Office of the Governor</li>
<li>Provincial Administrator</li>
<li>Department of Finance</li>
<li>Department of Works</li>
<li>Department of Health</li>
<li>Department of Education</li>
<li>Department of Agriculture</li>
<li>Department of Commerce</li>
<li>Department of Lands</li>
<li>Department of Environment</li>
<li>Provincial Planning Office</li>
<li>Internal Audit Division</li>
</ul>";
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Administrative Structure', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage the organizational structure of Provincial Government', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <form method="post" action="">
        <?php wp_nonce_field('provincial_structure_nonce', 'provincial_nonce'); ?>
        
        <div class="esp-form-section">
            <h3><?php _e('Government Sectors', 'esp-admin-manager'); ?></h3>
            <p><?php _e('List the main sectors and services provided by the provincial government.', 'esp-admin-manager'); ?></p>
            
            <div class="esp-form-row">
                <label for="government_sectors"><?php _e('Sectors & Services', 'esp-admin-manager'); ?></label>
                <div>
                    <?php
                    $sectors_content = isset($structure['government_sectors']) ? $structure['government_sectors'] : $default_government_sectors;
                    wp_editor($sectors_content, 'government_sectors', array(
                        'textarea_name' => 'government_sectors',
                        'media_buttons' => false,
                        'textarea_rows' => 15,
                        'teeny' => false,
                        'quicktags' => true,
                        'tinymce' => array(
                            'toolbar1' => 'bold,italic,underline,bullist,numlist,link,unlink,undo,redo',
                            'toolbar2' => ''
                        )
                    ));
                    ?>
                    <div class="description">
                        <?php _e('Use bullet points or numbered lists to organize the sectors. You can use basic formatting like bold and italic.', 'esp-admin-manager'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Administrative Divisions', 'esp-admin-manager'); ?></h3>
            <p><?php _e('List the main departments and offices within the provincial administration.', 'esp-admin-manager'); ?></p>
            
            <div class="esp-form-row">
                <label for="administrative_divisions"><?php _e('Departments & Offices', 'esp-admin-manager'); ?></label>
                <div>
                    <?php
                    $divisions_content = isset($structure['administrative_divisions']) ? $structure['administrative_divisions'] : $default_administrative_divisions;
                    wp_editor($divisions_content, 'administrative_divisions', array(
                        'textarea_name' => 'administrative_divisions',
                        'media_buttons' => false,
                        'textarea_rows' => 15,
                        'teeny' => false,
                        'quicktags' => true,
                        'tinymce' => array(
                            'toolbar1' => 'bold,italic,underline,bullist,numlist,link,unlink,undo,redo',
                            'toolbar2' => ''
                        )
                    ));
                    ?>
                    <div class="description">
                        <?php _e('List departments in order of importance or hierarchy. Include key offices and divisions.', 'esp-admin-manager'); ?>
                    </div>
                </div>
            </div>
        </div>

        <p class="submit">
            <input type="submit" name="submit" class="button-primary esp-button large" 
                   value="<?php _e('Save Administrative Structure', 'esp-admin-manager'); ?>" />
        </p>
    </form>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the administrative structure will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px;">
            <?php echo Provincial_Frontend::get_admin_structure_html(); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong>
            <code>[dakoii_prov_admin_structure]</code> <?php _e('(Primary)', 'esp-admin-manager'); ?>
            <small style="color: #666; margin-left: 10px;"><?php _e('or', 'esp-admin-manager'); ?> <code>[dakoii_admin_structure]</code>, <code>[esp_admin_structure]</code></small>
        </p>
        <p>
            <?php _e('Copy and paste this shortcode into any page or post to display the administrative structure.', 'esp-admin-manager'); ?>
        </p>
    </div>

    <!-- Quick Reference -->
    <div class="esp-form-section">
        <h3><?php _e('Structure Overview', 'esp-admin-manager'); ?></h3>
        <div class="esp-dashboard-cards">
            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">🏛️</span>
                <h3><?php _e('Government Sectors', 'esp-admin-manager'); ?></h3>
                <div class="count">
                    <?php 
                    $sectors_text = strip_tags($structure['government_sectors'] ?? $default_government_sectors);
                    $sectors_count = substr_count($sectors_text, "\n") + 1;
                    echo $sectors_count;
                    ?>
                </div>
                <div class="description"><?php _e('Service areas covered', 'esp-admin-manager'); ?></div>
            </div>

            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">🏢</span>
                <h3><?php _e('Administrative Divisions', 'esp-admin-manager'); ?></h3>
                <div class="count">
                    <?php 
                    $divisions_text = strip_tags($structure['administrative_divisions'] ?? $default_administrative_divisions);
                    $divisions_count = substr_count($divisions_text, "\n") + 1;
                    echo $divisions_count;
                    ?>
                </div>
                <div class="description"><?php _e('Departments and offices', 'esp-admin-manager'); ?></div>
            </div>

            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">📋</span>
                <h3><?php _e('Last Updated', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 14px;">
                    <?php echo date('M j, Y'); ?>
                </div>
                <div class="description"><?php _e('Structure information', 'esp-admin-manager'); ?></div>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Administrative Structure Help', 'esp-admin-manager'); ?></h4>
        <p><?php _e('The administrative structure helps citizens understand how the provincial government is organized. Consider these guidelines:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Keep the structure current and reflect any organizational changes', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use clear, descriptive names for departments and sectors', 'esp-admin-manager'); ?></li>
            <li><?php _e('Organize items logically, perhaps by importance or alphabetically', 'esp-admin-manager'); ?></li>
            <li><?php _e('Consider adding brief descriptions for complex department names', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use bullet points for easy reading and scanning', 'esp-admin-manager'); ?></li>
            <li><?php _e('Review and update regularly to maintain accuracy', 'esp-admin-manager'); ?></li>
        </ul>
    </div>

    <!-- Template Examples -->
    <div class="esp-form-section">
        <h3><?php _e('Template Examples', 'esp-admin-manager'); ?></h3>
        <p><?php _e('Here are some formatting examples you can use:', 'esp-admin-manager'); ?></p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4><?php _e('Simple List Format', 'esp-admin-manager'); ?></h4>
                <div style="background: #f9f9f9; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                    &lt;ul&gt;<br>
                    &lt;li&gt;Department Name&lt;/li&gt;<br>
                    &lt;li&gt;Another Department&lt;/li&gt;<br>
                    &lt;/ul&gt;
                </div>
            </div>
            
            <div>
                <h4><?php _e('Hierarchical Format', 'esp-admin-manager'); ?></h4>
                <div style="background: #f9f9f9; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                    &lt;ul&gt;<br>
                    &lt;li&gt;&lt;strong&gt;Main Department&lt;/strong&gt;<br>
                    &nbsp;&nbsp;&lt;ul&gt;<br>
                    &nbsp;&nbsp;&lt;li&gt;Sub-division&lt;/li&gt;<br>
                    &nbsp;&nbsp;&lt;/ul&gt;<br>
                    &lt;/li&gt;<br>
                    &lt;/ul&gt;
                </div>
            </div>
        </div>
    </div>
</div>
