<?php
/**
 * Provincial Administration Manager - Dashboard View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get data counts
$governor_count = wp_count_posts('esp_governor')->publish ?? 0;
$mp_count = wp_count_posts('esp_mp')->publish ?? 0;
$district_count = wp_count_posts('esp_district')->publish ?? 0;
$event_count = wp_count_posts('esp_event')->publish ?? 0;
$news_count = wp_count_posts('esp_news')->publish ?? 0;

// Get statistics
$statistics = get_option('esp_provincial_statistics', array());
$contact = get_option('esp_contact_information', array());
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Provincial Administration Dashboard', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Central management system for Provincial Administration data', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <!-- Quick Stats -->
    <div class="esp-dashboard-cards">
        <div class="esp-dashboard-card">
            <span class="esp-dashboard-card-icon">👤</span>
            <h3><?php _e('Governor Profile', 'esp-admin-manager'); ?></h3>
            <div class="count"><?php echo esc_html($governor_count); ?></div>
            <div class="description"><?php _e('Governor profile and information', 'esp-admin-manager'); ?></div>
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-governor'); ?>" class="button">
                <?php _e('Manage Governor', 'esp-admin-manager'); ?>
            </a>
        </div>

        <div class="esp-dashboard-card">
            <span class="esp-dashboard-card-icon">🏛️</span>
            <h3><?php _e('Members of Parliament', 'esp-admin-manager'); ?></h3>
            <div class="count"><?php echo esc_html($mp_count); ?></div>
            <div class="description"><?php _e('MP profiles and contact information', 'esp-admin-manager'); ?></div>
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-mps'); ?>" class="button">
                <?php _e('Manage MPs', 'esp-admin-manager'); ?>
            </a>
        </div>

        <div class="esp-dashboard-card">
            <span class="esp-dashboard-card-icon">🗺️</span>
            <h3><?php _e('Districts', 'esp-admin-manager'); ?></h3>
            <div class="count"><?php echo esc_html($district_count); ?></div>
            <div class="description"><?php _e('District information and statistics', 'esp-admin-manager'); ?></div>
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts'); ?>" class="button">
                <?php _e('Manage Districts', 'esp-admin-manager'); ?>
            </a>
        </div>

        <div class="esp-dashboard-card">
            <span class="esp-dashboard-card-icon">🎉</span>
            <h3><?php _e('Events', 'esp-admin-manager'); ?></h3>
            <div class="count"><?php echo esc_html($event_count); ?></div>
            <div class="description"><?php _e('Government events and activities', 'esp-admin-manager'); ?></div>
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-events'); ?>" class="button">
                <?php _e('Manage Events', 'esp-admin-manager'); ?>
            </a>
        </div>

        <div class="esp-dashboard-card">
            <span class="esp-dashboard-card-icon">📰</span>
            <h3><?php _e('News', 'esp-admin-manager'); ?></h3>
            <div class="count"><?php echo esc_html($news_count); ?></div>
            <div class="description"><?php _e('Government news and announcements', 'esp-admin-manager'); ?></div>
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-news'); ?>" class="button">
                <?php _e('Manage News', 'esp-admin-manager'); ?>
            </a>
        </div>

        <div class="esp-dashboard-card">
            <span class="esp-dashboard-card-icon">📊</span>
            <h3><?php _e('Statistics', 'esp-admin-manager'); ?></h3>
            <div class="count"><?php echo !empty($statistics) ? count($statistics) : 0; ?></div>
            <div class="description"><?php _e('Provincial statistics and data', 'esp-admin-manager'); ?></div>
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-statistics'); ?>" class="button">
                <?php _e('Manage Statistics', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="esp-quick-actions">
        <h3><?php _e('Quick Actions', 'esp-admin-manager'); ?></h3>
        <div class="actions">
            <a href="<?php echo admin_url('post-new.php?post_type=esp_mp'); ?>">
                <?php _e('Add New MP', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('post-new.php?post_type=esp_district'); ?>">
                <?php _e('Add New District', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('post-new.php?post_type=esp_event'); ?>">
                <?php _e('Add New Event', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('post-new.php?post_type=esp_news'); ?>">
                <?php _e('Add News Article', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-contact'); ?>">
                <?php _e('Update Contact Info', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- Current Statistics Overview -->
    <?php if (!empty($statistics)): ?>
    <div class="esp-form-section">
        <h3><?php _e('Current Provincial Statistics', 'esp-admin-manager'); ?></h3>
        <div class="esp-stats-grid">
            <?php if (isset($statistics['population'])): ?>
            <div class="esp-stats-item">
                <label><?php _e('Population', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo esc_html($statistics['population']); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($statistics['area'])): ?>
            <div class="esp-stats-item">
                <label><?php _e('Area (km²)', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo esc_html($statistics['area']); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($statistics['districts'])): ?>
            <div class="esp-stats-item">
                <label><?php _e('Districts', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo esc_html($statistics['districts']); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($statistics['llgs'])): ?>
            <div class="esp-stats-item">
                <label><?php _e('LLGs', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo esc_html($statistics['llgs']); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($statistics['wards'])): ?>
            <div class="esp-stats-item">
                <label><?php _e('Wards', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo esc_html($statistics['wards']); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($statistics['urban_llgs'])): ?>
            <div class="esp-stats-item">
                <label><?php _e('Urban LLGs', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo esc_html($statistics['urban_llgs']); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Activity -->
    <div class="esp-form-section">
        <h3><?php _e('Recent Activity', 'esp-admin-manager'); ?></h3>
        
        <?php
        // Get recent posts from all ESP post types
        $recent_posts = get_posts(array(
            'post_type' => array('esp_mp', 'esp_district', 'esp_event', 'esp_news'),
            'numberposts' => 10,
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        ?>

        <?php if (!empty($recent_posts)): ?>
        <table class="esp-list-table">
            <thead>
                <tr>
                    <th><?php _e('Type', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Title', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Date', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($recent_posts as $post): ?>
                <tr>
                    <td>
                        <?php
                        $type_labels = array(
                            'esp_mp' => __('MP', 'esp-admin-manager'),
                            'esp_district' => __('District', 'esp-admin-manager'),
                            'esp_event' => __('Event', 'esp-admin-manager'),
                            'esp_news' => __('News', 'esp-admin-manager')
                        );
                        echo esc_html($type_labels[$post->post_type] ?? $post->post_type);
                        ?>
                    </td>
                    <td><?php echo esc_html($post->post_title); ?></td>
                    <td><?php echo esc_html(get_the_date('M j, Y', $post)); ?></td>
                    <td class="actions">
                        <a href="<?php echo get_edit_post_link($post->ID); ?>" class="edit">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <p><?php _e('No recent activity found.', 'esp-admin-manager'); ?></p>
        <?php endif; ?>
    </div>

    <!-- Shortcode Help -->
    <div class="esp-help">
        <h4><?php _e('Using Shortcodes', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Display Dakoii Provincial Administration data on your website using these shortcodes. All shortcodes are fully compatible and produce the same output.', 'esp-admin-manager'); ?></p>

        <h5 style="margin: 15px 0 5px 0; color: var(--esp-primary);"><?php _e('Primary Shortcodes (Recommended)', 'esp-admin-manager'); ?></h5>
        <p style="font-size: 13px; color: #666; margin: 0 0 10px 0;"><?php _e('Full plugin name shortcodes - most descriptive and clear:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 5px 0 0 20px;">
            <li><code>[dakoii_prov_admin_governor]</code> - <?php _e('Display governor profile', 'esp-admin-manager'); ?></li>
            <li><code>[dakoii_prov_admin_mps]</code> - <?php _e('Display all MPs', 'esp-admin-manager'); ?></li>
            <li><code>[dakoii_prov_admin_districts]</code> - <?php _e('Display all districts', 'esp-admin-manager'); ?></li>
            <li><code>[dakoii_prov_admin_statistics]</code> - <?php _e('Display provincial statistics', 'esp-admin-manager'); ?></li>
            <li><code>[dakoii_prov_admin_structure]</code> - <?php _e('Display administrative structure', 'esp-admin-manager'); ?></li>
            <li><code>[dakoii_prov_admin_events]</code> - <?php _e('Display upcoming events', 'esp-admin-manager'); ?></li>
            <li><code>[dakoii_prov_admin_news]</code> - <?php _e('Display latest news', 'esp-admin-manager'); ?></li>
            <li><code>[dakoii_prov_admin_contact]</code> - <?php _e('Display contact information', 'esp-admin-manager'); ?></li>
            <li><code>[dakoii_prov_admin_slideshow]</code> - <?php _e('Display slideshow', 'esp-admin-manager'); ?></li>
        </ul>

        <h5 style="margin: 15px 0 5px 0; color: var(--esp-primary);"><?php _e('Alternative Shortcodes (Also Recommended)', 'esp-admin-manager'); ?></h5>
        <p style="font-size: 13px; color: #666; margin: 0 0 10px 0;"><?php _e('Shorter Dakoii shortcodes - easier to type:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 5px 0 0 20px;">
            <li><code>[dakoii_governor]</code>, <code>[dakoii_mps]</code>, <code>[dakoii_districts]</code>, <code>[dakoii_statistics]</code></li>
            <li><code>[dakoii_admin_structure]</code>, <code>[dakoii_events]</code>, <code>[dakoii_news]</code>, <code>[dakoii_contact]</code>, <code>[dakoii_slideshow]</code></li>
        </ul>

        <details style="margin-top: 15px;">
            <summary style="cursor: pointer; color: #666; font-size: 13px;"><?php _e('Additional Compatible Shortcodes', 'esp-admin-manager'); ?></summary>
            <div style="padding: 10px 0;">
                <strong style="font-size: 12px;"><?php _e('Generic Provincial Shortcodes:', 'esp-admin-manager'); ?></strong>
                <ul style="margin: 5px 0 10px 20px; font-size: 12px; color: #888;">
                    <li><code>[provincial_governor]</code>, <code>[provincial_mps]</code>, <code>[provincial_districts]</code>, <code>[provincial_statistics]</code></li>
                    <li><code>[provincial_admin_structure]</code>, <code>[provincial_events]</code>, <code>[provincial_news]</code>, <code>[provincial_contact]</code>, <code>[provincial_slideshow]</code></li>
                </ul>
                <strong style="font-size: 12px;"><?php _e('Legacy ESP Shortcodes (Backward Compatibility):', 'esp-admin-manager'); ?></strong>
                <ul style="margin: 5px 0 0 20px; font-size: 12px; color: #888;">
                    <li><code>[esp_governor]</code>, <code>[esp_mps]</code>, <code>[esp_districts]</code>, <code>[esp_statistics]</code></li>
                    <li><code>[esp_admin_structure]</code>, <code>[esp_events]</code>, <code>[esp_news]</code>, <code>[esp_contact]</code>, <code>[esp_slideshow]</code></li>
                </ul>
            </div>
        </details>
    </div>

    <!-- Debug Section (for administrators) -->
    <?php if (current_user_can('manage_options')): ?>
    <div class="esp-help">
        <h4><?php _e('Debug & Troubleshooting', 'esp-admin-manager'); ?></h4>
        <p><?php _e('If you\'re experiencing issues with the MPs feature (edit buttons, add new MP, etc.), use the debug helper:', 'esp-admin-manager'); ?></p>
        <p>
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-dashboard&debug=1'); ?>" class="button button-secondary">
                <?php _e('Open Debug Helper', 'esp-admin-manager'); ?>
            </a>
        </p>
    </div>
    <?php endif; ?>
</div>
