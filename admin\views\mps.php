<?php
/**
 * Provincial Administration Manager - MPs Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get all MPs
$mps = get_posts(array(
    'post_type' => 'esp_mp',
    'numberposts' => -1,
    'post_status' => 'any',
    'orderby' => 'title',
    'order' => 'ASC'
));
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Members of Parliament', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage MP profiles, contact information, and electoral details', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <!-- Quick Actions -->
    <div class="esp-quick-actions">
        <h3><?php _e('Quick Actions', 'esp-admin-manager'); ?></h3>
        <div class="actions">
            <a href="<?php echo admin_url('post-new.php?post_type=esp_mp'); ?>" class="esp-button">
                <?php _e('Add New MP', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('edit.php?post_type=esp_mp'); ?>" class="esp-button secondary">
                <?php _e('Manage All MPs', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- MPs List -->
    <div class="esp-form-section">
        <h3><?php _e('Current Members of Parliament', 'esp-admin-manager'); ?></h3>
        
        <?php if (!empty($mps)): ?>
        <table class="esp-list-table">
            <thead>
                <tr>
                    <th><?php _e('Photo', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Name', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Electorate', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Party', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Contact', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Status', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($mps as $mp): 
                    $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
                    $party = get_post_meta($mp->ID, '_esp_mp_party', true);
                    $email = get_post_meta($mp->ID, '_esp_mp_email', true);
                    $phone = get_post_meta($mp->ID, '_esp_mp_phone', true);
                ?>
                <tr>
                    <td>
                        <?php if (has_post_thumbnail($mp->ID)): ?>
                            <?php echo get_the_post_thumbnail($mp->ID, array(50, 50)); ?>
                        <?php else: ?>
                            <div style="width: 50px; height: 50px; background: var(--esp-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                                👤
                            </div>
                        <?php endif; ?>
                    </td>
                    <td>
                        <strong><?php echo esc_html($mp->post_title); ?></strong>
                        <div style="font-size: 12px; color: #666;">
                            <?php echo esc_html(get_the_date('M j, Y', $mp)); ?>
                        </div>
                    </td>
                    <td><?php echo esc_html($electorate); ?></td>
                    <td>
                        <?php if ($party): ?>
                            <span style="background: var(--esp-light); padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                                <?php echo esc_html($party); ?>
                            </span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($email): ?>
                            <div style="font-size: 12px;">
                                <a href="mailto:<?php echo esc_attr($email); ?>"><?php echo esc_html($email); ?></a>
                            </div>
                        <?php endif; ?>
                        <?php if ($phone): ?>
                            <div style="font-size: 12px; color: #666;">
                                <?php echo esc_html($phone); ?>
                            </div>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($mp->post_status === 'publish'): ?>
                            <span style="color: green;">●</span> <?php _e('Published', 'esp-admin-manager'); ?>
                        <?php else: ?>
                            <span style="color: orange;">●</span> <?php echo esc_html(ucfirst($mp->post_status)); ?>
                        <?php endif; ?>
                    </td>
                    <td class="actions">
                        <a href="<?php echo get_edit_post_link($mp->ID); ?>" class="edit">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </a>
                        <a href="<?php echo get_delete_post_link($mp->ID); ?>" class="delete" 
                           onclick="return confirm('<?php _e('Are you sure you want to delete this MP?', 'esp-admin-manager'); ?>')">
                            <?php _e('Delete', 'esp-admin-manager'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div class="esp-message warning">
            <p><?php _e('No MPs found. Click "Add New MP" to get started.', 'esp-admin-manager'); ?></p>
        </div>
        <?php endif; ?>
    </div>

    <!-- Statistics -->
    <div class="esp-form-section">
        <h3><?php _e('MP Statistics', 'esp-admin-manager'); ?></h3>
        <div class="esp-stats-grid">
            <div class="esp-stats-item">
                <label><?php _e('Total MPs', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo count($mps); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Published', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: green;">
                    <?php echo count(array_filter($mps, function($mp) { return $mp->post_status === 'publish'; })); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('With Photos', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-primary);">
                    <?php echo count(array_filter($mps, function($mp) { return has_post_thumbnail($mp->ID); })); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Unique Parties', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-accent);">
                    <?php 
                    $parties = array_unique(array_filter(array_map(function($mp) {
                        return get_post_meta($mp->ID, '_esp_mp_party', true);
                    }, $mps)));
                    echo count($parties);
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the MPs will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px; max-height: 400px; overflow-y: auto;">
            <?php echo do_shortcode('[esp_mps limit="6"]'); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong>
            <code>[dakoii_prov_admin_mps]</code> <?php _e('(Primary)', 'esp-admin-manager'); ?>
            <small style="color: #666; margin-left: 10px;"><?php _e('or', 'esp-admin-manager'); ?> <code>[dakoii_mps]</code>, <code>[esp_mps]</code></small>
        </p>
        <p>
            <?php _e('Options:', 'esp-admin-manager'); ?>
            <code>[dakoii_prov_admin_mps limit="6"]</code> - <?php _e('Show only 6 MPs', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_mps show_photos="false"]</code> - <?php _e('Hide photos', 'esp-admin-manager'); ?>
        </p>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Managing MPs', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Here are some tips for managing MP profiles effectively:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Keep MP information current, especially after elections', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use high-quality, professional photos for all MPs', 'esp-admin-manager'); ?></li>
            <li><?php _e('Include accurate electorate and party information', 'esp-admin-manager'); ?></li>
            <li><?php _e('Provide contact information when available and appropriate', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use the "Draft" status for MPs who are not yet confirmed', 'esp-admin-manager'); ?></li>
            <li><?php _e('Regular updates help maintain public trust and transparency', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>
