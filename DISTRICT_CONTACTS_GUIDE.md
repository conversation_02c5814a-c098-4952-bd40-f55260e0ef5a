# District Contacts System - Implementation Guide

## Overview
The District Contacts System allows each district to have its own contact information that can be displayed using shortcodes. This extends the existing provincial contact system to support multiple district-specific contact sets.

## Features
- ✅ Each district has its own complete contact information
- ✅ Multiple shortcode options for flexibility
- ✅ Consistent styling with existing provincial contacts
- ✅ Easy management through WordPress admin
- ✅ Backward compatibility with existing provincial contacts

## How to Add District Contact Information

### 1. Edit a District
1. Go to WordPress Admin → Districts (or Posts → Districts)
2. Edit an existing district or create a new one
3. Scroll down to the "District Contact Information" section
4. Fill in the contact fields:
   - **District Office Address**: Physical address of the district office
   - **Phone Number**: Main district office phone
   - **Fax Number**: District fax number
   - **Emergency Contact**: Emergency contact number
   - **General Email**: General inquiries email
   - **Administrative Email**: Official business email
   - **Website**: District website URL (if available)
   - **Office Hours**: District office operating hours

### 2. Save the District
Click "Update" or "Publish" to save the contact information.

## Available Shortcodes

### Option 1: Extended Contact Shortcode
Use the existing contact shortcode with a district_id parameter:

```
[dakoii_contact district_id="123"]
```

Replace "123" with the actual district ID.

### Option 2: District-Specific Contact Shortcode
Use the dedicated district contact shortcode:

```
[dakoii_district_contact id="123"]
```

Replace "123" with the actual district ID.

### Option 3: Alternative Naming Conventions
All these shortcodes work identically:

**Primary (Recommended):**
- `[dakoii_prov_admin_contact district_id="123"]`
- `[dakoii_prov_admin_district_contact id="123"]`

**Alternative Shorter:**
- `[dakoii_contact district_id="123"]`
- `[dakoii_district_contact id="123"]`

**Generic Provincial:**
- `[provincial_contact district_id="123"]`
- `[provincial_district_contact id="123"]`

**Legacy ESP:**
- `[esp_contact district_id="123"]`
- `[esp_district_contact id="123"]`

## Layout Options

Both shortcodes support layout options:

```
[dakoii_contact district_id="123" layout="grid"]
[dakoii_contact district_id="123" layout="list"]
```

```
[dakoii_district_contact id="123" layout="grid"]
[dakoii_district_contact id="123" layout="list"]
```

## Finding District IDs

### Method 1: WordPress Admin
1. Go to Districts management
2. Edit a district
3. Look at the URL: `post.php?post=123&action=edit` - the number is the district ID

### Method 2: Districts List
1. Go to Districts management page
2. The district ID is shown in the Actions column or can be found by hovering over the district name

## Examples

### Example 1: Display Wewak District Contacts
```
[dakoii_contact district_id="45"]
```

### Example 2: Display Maprik District Contacts in List Layout
```
[dakoii_district_contact id="67" layout="list"]
```

### Example 3: Provincial Contacts (No Change)
```
[dakoii_contact]
```
This still displays provincial-level contacts as before.

## Technical Details

### Database Structure
District contact information is stored as post meta fields:
- `_esp_district_contact_address`
- `_esp_district_contact_phone`
- `_esp_district_contact_fax`
- `_esp_district_contact_emergency`
- `_esp_district_contact_email`
- `_esp_district_contact_admin_email`
- `_esp_district_contact_website`
- `_esp_district_contact_office_hours`

### Error Handling
- Invalid district IDs show appropriate error messages
- Empty contact information shows "No contact information found" message
- Non-existent districts are handled gracefully

### Permissions
- Same permission system as existing shortcodes
- District-level users can only see their assigned districts
- Provincial-level users can see all districts
- Public users can see all published content

## Backward Compatibility
- Existing provincial contact shortcodes work unchanged
- All existing shortcode variations continue to function
- No changes to existing provincial contact management

## Testing the Implementation

1. Create or edit a district
2. Add contact information in the "District Contact Information" section
3. Save the district
4. Use the shortcode on a page or post: `[dakoii_contact district_id="YOUR_DISTRICT_ID"]`
5. View the page to see the district contacts displayed

## Support
If you encounter any issues:
1. Verify the district ID is correct
2. Ensure the district has contact information filled in
3. Check that the district is published
4. Verify shortcode syntax is correct
